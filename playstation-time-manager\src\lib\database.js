import { supabase } from './supabase'

// دوال إدارة العملاء
export const customerService = {
  // إضافة عميل جديد
  async createCustomer(customerData) {
    const { data, error } = await supabase
      .from('customers')
      .insert([customerData])
      .select()
    
    if (error) throw error
    return data[0]
  },

  // الحصول على جميع العملاء
  async getAllCustomers() {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // الحصول على عميل بالمعرف
  async getCustomerById(id) {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data
  },

  // تحديث رصيد العميل
  async updateCustomerBalance(id, newBalance) {
    const { data, error } = await supabase
      .from('customers')
      .update({ balance: newBalance, updated_at: new Date() })
      .eq('id', id)
      .select()
    
    if (error) throw error
    return data[0]
  }
}

// دوال إدارة الأجهزة
export const deviceService = {
  // الحصول على جميع الأجهزة مع أنواعها
  async getAllDevices() {
    const { data, error } = await supabase
      .from('devices')
      .select(`
        *,
        device_types (
          name,
          hourly_rate
        )
      `)
      .order('name')
    
    if (error) throw error
    return data
  },

  // تحديث حالة الجهاز
  async updateDeviceStatus(id, status) {
    const { data, error } = await supabase
      .from('devices')
      .update({ status, updated_at: new Date() })
      .eq('id', id)
      .select()
    
    if (error) throw error
    return data[0]
  },

  // الحصول على الأجهزة المتاحة
  async getAvailableDevices() {
    const { data, error } = await supabase
      .from('devices')
      .select(`
        *,
        device_types (
          name,
          hourly_rate
        )
      `)
      .eq('status', 'available')
      .order('name')
    
    if (error) throw error
    return data
  }
}

// دوال إدارة الجلسات
export const sessionService = {
  // بدء جلسة جديدة
  async startSession(sessionData) {
    const { data, error } = await supabase
      .from('sessions')
      .insert([{
        ...sessionData,
        start_time: new Date(),
        status: 'active'
      }])
      .select()
    
    if (error) throw error
    
    // تحديث حالة الجهاز إلى مشغول
    await deviceService.updateDeviceStatus(sessionData.device_id, 'occupied')
    
    return data[0]
  },

  // إنهاء الجلسة
  async endSession(sessionId) {
    const endTime = new Date()
    
    // الحصول على بيانات الجلسة
    const { data: session, error: sessionError } = await supabase
      .from('sessions')
      .select('*, devices(*), device_types(*)')
      .eq('id', sessionId)
      .single()
    
    if (sessionError) throw sessionError
    
    // حساب المدة والتكلفة
    const startTime = new Date(session.start_time)
    const durationMinutes = Math.ceil((endTime - startTime) / (1000 * 60))
    const totalCost = (durationMinutes / 60) * session.hourly_rate
    
    // تحديث الجلسة
    const { data, error } = await supabase
      .from('sessions')
      .update({
        end_time: endTime,
        duration_minutes: durationMinutes,
        total_cost: totalCost,
        status: 'completed',
        updated_at: new Date()
      })
      .eq('id', sessionId)
      .select()
    
    if (error) throw error
    
    // تحديث حالة الجهاز إلى متاح
    await deviceService.updateDeviceStatus(session.device_id, 'available')
    
    return data[0]
  },

  // الحصول على الجلسات النشطة
  async getActiveSessions() {
    const { data, error } = await supabase
      .from('sessions')
      .select(`
        *,
        customers (name, phone),
        devices (name),
        device_types (name, hourly_rate)
      `)
      .eq('status', 'active')
      .order('start_time', { ascending: false })
    
    if (error) throw error
    return data
  },

  // الحصول على جميع الجلسات
  async getAllSessions(limit = 50) {
    const { data, error } = await supabase
      .from('sessions')
      .select(`
        *,
        customers (name, phone),
        devices (name),
        device_types (name)
      `)
      .order('start_time', { ascending: false })
      .limit(limit)
    
    if (error) throw error
    return data
  }
}

// دوال التقارير
export const reportService = {
  // إحصائيات اليوم
  async getTodayStats() {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const { data, error } = await supabase
      .from('sessions')
      .select('total_cost, duration_minutes')
      .gte('start_time', today.toISOString())
      .eq('status', 'completed')
    
    if (error) throw error
    
    const totalRevenue = data.reduce((sum, session) => sum + (session.total_cost || 0), 0)
    const totalHours = data.reduce((sum, session) => sum + (session.duration_minutes || 0), 0) / 60
    
    return {
      totalRevenue,
      totalHours,
      totalSessions: data.length
    }
  }
}
