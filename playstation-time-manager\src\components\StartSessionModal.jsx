import React, { useState, useEffect } from 'react'
import { X, Play } from 'lucide-react'
import { customerService, deviceService, sessionService } from '../lib/database'

const StartSessionModal = ({ isOpen, onClose, onSessionStarted }) => {
  const [customers, setCustomers] = useState([])
  const [devices, setDevices] = useState([])
  const [selectedCustomer, setSelectedCustomer] = useState('')
  const [selectedDevice, setSelectedDevice] = useState('')
  const [newCustomerName, setNewCustomerName] = useState('')
  const [newCustomerPhone, setNewCustomerPhone] = useState('')
  const [isCreatingCustomer, setIsCreatingCustomer] = useState(false)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen) {
      loadData()
    }
  }, [isOpen])

  const loadData = async () => {
    try {
      const [customersData, devicesData] = await Promise.all([
        customerService.getAllCustomers(),
        deviceService.getAvailableDevices()
      ])
      setCustomers(customersData)
      setDevices(devicesData)
    } catch (error) {
      console.error('Error loading data:', error)
    }
  }

  const handleCreateCustomer = async () => {
    if (!newCustomerName.trim()) return

    try {
      setLoading(true)
      const newCustomer = await customerService.createCustomer({
        name: newCustomerName.trim(),
        phone: newCustomerPhone.trim() || null
      })
      
      setCustomers([newCustomer, ...customers])
      setSelectedCustomer(newCustomer.id)
      setNewCustomerName('')
      setNewCustomerPhone('')
      setIsCreatingCustomer(false)
    } catch (error) {
      console.error('Error creating customer:', error)
      alert('حدث خطأ في إنشاء العميل')
    } finally {
      setLoading(false)
    }
  }

  const handleStartSession = async () => {
    if (!selectedCustomer || !selectedDevice) {
      alert('يرجى اختيار العميل والجهاز')
      return
    }

    try {
      setLoading(true)
      const selectedDeviceData = devices.find(d => d.id === selectedDevice)
      
      const session = await sessionService.startSession({
        customer_id: selectedCustomer,
        device_id: selectedDevice,
        hourly_rate: selectedDeviceData.device_types.hourly_rate
      })

      onSessionStarted(session)
      onClose()
      resetForm()
    } catch (error) {
      console.error('Error starting session:', error)
      alert('حدث خطأ في بدء الجلسة')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setSelectedCustomer('')
    setSelectedDevice('')
    setNewCustomerName('')
    setNewCustomerPhone('')
    setIsCreatingCustomer(false)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold">بدء جلسة جديدة</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>

        <div className="space-y-4">
          {/* اختيار العميل */}
          <div>
            <label className="block text-sm font-medium mb-2">العميل</label>
            {!isCreatingCustomer ? (
              <div className="space-y-2">
                <select
                  value={selectedCustomer}
                  onChange={(e) => setSelectedCustomer(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">اختر عميل</option>
                  {customers.map((customer) => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name} {customer.phone && `(${customer.phone})`}
                    </option>
                  ))}
                </select>
                <button
                  onClick={() => setIsCreatingCustomer(true)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  + إضافة عميل جديد
                </button>
              </div>
            ) : (
              <div className="space-y-2">
                <input
                  type="text"
                  placeholder="اسم العميل"
                  value={newCustomerName}
                  onChange={(e) => setNewCustomerName(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
                <input
                  type="text"
                  placeholder="رقم الهاتف (اختياري)"
                  value={newCustomerPhone}
                  onChange={(e) => setNewCustomerPhone(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
                <div className="flex space-x-reverse space-x-2">
                  <button
                    onClick={handleCreateCustomer}
                    disabled={loading || !newCustomerName.trim()}
                    className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
                  >
                    إضافة
                  </button>
                  <button
                    onClick={() => setIsCreatingCustomer(false)}
                    className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm"
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* اختيار الجهاز */}
          <div>
            <label className="block text-sm font-medium mb-2">الجهاز</label>
            <select
              value={selectedDevice}
              onChange={(e) => setSelectedDevice(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">اختر جهاز</option>
              {devices.map((device) => (
                <option key={device.id} value={device.id}>
                  {device.name} - {device.device_types.name} ({device.device_types.hourly_rate} ج.م/ساعة)
                </option>
              ))}
            </select>
          </div>

          {/* أزرار التحكم */}
          <div className="flex space-x-reverse space-x-3 pt-4">
            <button
              onClick={handleStartSession}
              disabled={loading || !selectedCustomer || !selectedDevice}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center disabled:opacity-50"
            >
              <Play className="ml-2 h-4 w-4" />
              {loading ? 'جاري البدء...' : 'بدء الجلسة'}
            </button>
            <button
              onClick={onClose}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md"
            >
              إلغاء
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StartSessionModal
