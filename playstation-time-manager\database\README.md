# إعداد قاعدة البيانات - نظام إدارة الوقت لمحل البلايستيشن

## خطوات الإعداد

### 1. إنشاء حساب Supabase
1. اذهب إلى [supabase.com](https://supabase.com)
2. أنشئ حساب جديد أو سجل الدخول
3. أنشئ مشروع جديد

### 2. إعد<PERSON> قاعدة البيانات
1. في لوحة تحكم Supabase، اذهب إلى SQL Editor
2. انسخ محتوى ملف `schema.sql` والصقه في المحرر
3. اضغط على "Run" لتنفيذ الاستعلامات

### 3. إعداد متغيرات البيئة
1. في لوحة تحكم Supabase، اذهب إلى Settings > API
2. انسخ Project URL و anon public key
3. أنشئ ملف `.env` في جذر المشروع:

```env
VITE_SUPABASE_URL=your_project_url_here
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

### 4. تفعيل Row Level Security (اختياري)
لحماية إضافية، يمكنك تفعيل RLS على الجداول:

```sql
-- تفعيل RLS على جميع الجداول
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE device_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE special_rates ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسات للوصول الكامل (للتطوير)
CREATE POLICY "Enable all operations for all users" ON customers FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON devices FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON device_types FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON sessions FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON transactions FOR ALL USING (true);
CREATE POLICY "Enable all operations for all users" ON special_rates FOR ALL USING (true);
```

## هيكل قاعدة البيانات

### الجداول الرئيسية:

1. **customers** - بيانات العملاء
2. **device_types** - أنواع الأجهزة والأسعار
3. **devices** - الأجهزة الفردية
4. **sessions** - جلسات اللعب
5. **transactions** - المعاملات المالية
6. **special_rates** - الأسعار الخاصة والعروض

### العلاقات:
- كل جلسة مرتبطة بعميل وجهاز
- كل جهاز له نوع محدد
- كل معاملة مرتبطة بعميل وجلسة
- الأسعار الخاصة مرتبطة بأنواع الأجهزة

## البيانات الأولية

سيتم إدراج البيانات التالية تلقائياً:

### أنواع الأجهزة:
- PlayStation 4 (15 ج.م/ساعة)
- PlayStation 5 (25 ج.م/ساعة)
- Xbox Series X (20 ج.م/ساعة)
- Gaming PC (30 ج.م/ساعة)

### الأجهزة:
- 4 أجهزة PS4
- 2 جهاز PS5
- 2 جهاز Xbox
- 3 أجهزة PC

## ملاحظات مهمة

1. تأكد من نسخ URL و API Key بشكل صحيح
2. لا تشارك ملف `.env` مع أحد
3. استخدم `.env.example` كمرجع لإعداد البيئة
4. تأكد من تشغيل الاستعلامات في الترتيب الصحيح
