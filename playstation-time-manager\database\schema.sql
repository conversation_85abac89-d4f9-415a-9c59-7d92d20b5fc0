-- نظام إدارة الوقت لمحل البلايستيشن
-- تصميم قاعدة البيانات

-- جدول العملاء
CREATE TABLE customers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) UNIQUE,
    email VARCHAR(100),
    balance DECIMAL(10,2) DEFAULT 0.00,
    total_spent DECIMAL(10,2) DEFAULT 0.00,
    total_hours INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول أنواع الأجهزة
CREATE TABLE device_types (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(50) NOT NULL, -- PS4, PS5, Xbox, PC
    hourly_rate DECIMAL(8,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الأجهزة
CREATE TABLE devices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(50) NOT NULL, -- PS4-1, PS5-2, etc.
    device_type_id UUID REFERENCES device_types(id),
    status VARCHAR(20) DEFAULT 'available', -- available, occupied, maintenance
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الجلسات
CREATE TABLE sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    customer_id UUID REFERENCES customers(id),
    device_id UUID REFERENCES devices(id),
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    hourly_rate DECIMAL(8,2),
    total_cost DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'active', -- active, completed, cancelled
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المعاملات المالية
CREATE TABLE transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    customer_id UUID REFERENCES customers(id),
    session_id UUID REFERENCES sessions(id),
    type VARCHAR(20) NOT NULL, -- payment, refund, balance_add
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول الأسعار الخاصة (للعروض والخصومات)
CREATE TABLE special_rates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    device_type_id UUID REFERENCES device_types(id),
    name VARCHAR(100) NOT NULL,
    rate DECIMAL(8,2) NOT NULL,
    start_time TIME,
    end_time TIME,
    days_of_week INTEGER[], -- 0=Sunday, 1=Monday, etc.
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_sessions_customer_id ON sessions(customer_id);
CREATE INDEX idx_sessions_device_id ON sessions(device_id);
CREATE INDEX idx_sessions_start_time ON sessions(start_time);
CREATE INDEX idx_sessions_status ON sessions(status);
CREATE INDEX idx_transactions_customer_id ON transactions(customer_id);
CREATE INDEX idx_transactions_session_id ON transactions(session_id);
CREATE INDEX idx_devices_status ON devices(status);

-- إدراج بيانات أولية لأنواع الأجهزة
INSERT INTO device_types (name, hourly_rate) VALUES
('PlayStation 4', 15.00),
('PlayStation 5', 25.00),
('Xbox Series X', 20.00),
('Gaming PC', 30.00);

-- إدراج أجهزة أولية
INSERT INTO devices (name, device_type_id) 
SELECT 'PS4-' || generate_series(1,4), id FROM device_types WHERE name = 'PlayStation 4'
UNION ALL
SELECT 'PS5-' || generate_series(1,2), id FROM device_types WHERE name = 'PlayStation 5'
UNION ALL
SELECT 'Xbox-' || generate_series(1,2), id FROM device_types WHERE name = 'Xbox Series X'
UNION ALL
SELECT 'PC-' || generate_series(1,3), id FROM device_types WHERE name = 'Gaming PC';
