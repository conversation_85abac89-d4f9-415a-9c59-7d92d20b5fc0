import React, { useState, useEffect } from 'react'
import { Clock, Users, Monitor, DollarSign, Play, Square, Plus } from 'lucide-react'
import { sessionService, customerService, deviceService, reportService } from '../lib/database'
import StartSessionModal from '../components/StartSessionModal'

const Dashboard = () => {
  const [stats, setStats] = useState({
    activeSessions: 0,
    totalCustomers: 0,
    availableDevices: 0,
    todayRevenue: 0
  })
  const [activeSessions, setActiveSessions] = useState([])
  const [loading, setLoading] = useState(true)
  const [showStartSessionModal, setShowStartSessionModal] = useState(false)

  useEffect(() => {
    loadDashboardData()
    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(loadDashboardData, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadDashboardData = async () => {
    try {
      const [
        activeSessionsData,
        customersData,
        devicesData,
        todayStatsData
      ] = await Promise.all([
        sessionService.getActiveSessions(),
        customerService.getAllCustomers(),
        deviceService.getAvailableDevices(),
        reportService.getTodayStats()
      ])

      setStats({
        activeSessions: activeSessionsData.length,
        totalCustomers: customersData.length,
        availableDevices: devicesData.length,
        todayRevenue: todayStatsData.totalRevenue
      })

      setActiveSessions(activeSessionsData)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDuration = (startTime) => {
    const start = new Date(startTime)
    const now = new Date()
    const diffMinutes = Math.floor((now - start) / (1000 * 60))
    const hours = Math.floor(diffMinutes / 60)
    const minutes = diffMinutes % 60
    return `${hours}:${minutes.toString().padStart(2, '0')}`
  }

  const calculateCurrentCost = (startTime, hourlyRate) => {
    const start = new Date(startTime)
    const now = new Date()
    const diffHours = (now - start) / (1000 * 60 * 60)
    return (diffHours * hourlyRate).toFixed(2)
  }

  const handleSessionStarted = (newSession) => {
    loadDashboardData() // إعادة تحميل البيانات
  }

  const handleEndSession = async (sessionId) => {
    if (confirm('هل أنت متأكد من إنهاء هذه الجلسة؟')) {
      try {
        await sessionService.endSession(sessionId)
        loadDashboardData() // إعادة تحميل البيانات
      } catch (error) {
        console.error('Error ending session:', error)
        alert('حدث خطأ في إنهاء الجلسة')
      }
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-3xl font-bold">لوحة التحكم</h2>
        <button
          onClick={() => setShowStartSessionModal(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center"
        >
          <Plus className="ml-2 h-4 w-4" />
          بدء جلسة جديدة
        </button>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-700">الجلسات النشطة</h3>
              <p className="text-3xl font-bold text-blue-600">{stats.activeSessions}</p>
            </div>
            <Clock className="h-12 w-12 text-blue-600 opacity-20" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-700">إجمالي العملاء</h3>
              <p className="text-3xl font-bold text-green-600">{stats.totalCustomers}</p>
            </div>
            <Users className="h-12 w-12 text-green-600 opacity-20" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-700">الأجهزة المتاحة</h3>
              <p className="text-3xl font-bold text-yellow-600">{stats.availableDevices}</p>
            </div>
            <Monitor className="h-12 w-12 text-yellow-600 opacity-20" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-700">إيرادات اليوم</h3>
              <p className="text-3xl font-bold text-purple-600">{stats.todayRevenue.toFixed(2)} ج.م</p>
            </div>
            <DollarSign className="h-12 w-12 text-purple-600 opacity-20" />
          </div>
        </div>
      </div>

      {/* الجلسات النشطة */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-xl font-semibold flex items-center">
            <Play className="ml-2 h-5 w-5 text-green-500" />
            الجلسات النشطة
          </h3>
        </div>
        <div className="p-6">
          {activeSessions.length === 0 ? (
            <p className="text-gray-500 text-center py-8">لا توجد جلسات نشطة حالياً</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-4 font-semibold">العميل</th>
                    <th className="text-right py-3 px-4 font-semibold">الجهاز</th>
                    <th className="text-right py-3 px-4 font-semibold">وقت البداية</th>
                    <th className="text-right py-3 px-4 font-semibold">المدة</th>
                    <th className="text-right py-3 px-4 font-semibold">التكلفة الحالية</th>
                    <th className="text-right py-3 px-4 font-semibold">إجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {activeSessions.map((session) => (
                    <tr key={session.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">{session.customers?.name || 'غير محدد'}</td>
                      <td className="py-3 px-4">{session.devices?.name}</td>
                      <td className="py-3 px-4">
                        {new Date(session.start_time).toLocaleTimeString('ar-EG')}
                      </td>
                      <td className="py-3 px-4 font-mono">
                        {formatDuration(session.start_time)}
                      </td>
                      <td className="py-3 px-4 font-bold text-green-600">
                        {calculateCurrentCost(session.start_time, session.hourly_rate)} ج.م
                      </td>
                      <td className="py-3 px-4">
                        <button
                          onClick={() => handleEndSession(session.id)}
                          className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm flex items-center"
                        >
                          <Square className="ml-1 h-4 w-4" />
                          إنهاء
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* مودال بدء جلسة جديدة */}
      <StartSessionModal
        isOpen={showStartSessionModal}
        onClose={() => setShowStartSessionModal(false)}
        onSessionStarted={handleSessionStarted}
      />
    </div>
  )
}

export default Dashboard
