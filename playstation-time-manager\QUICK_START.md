# دليل البدء السريع - نظام إدارة الوقت لمحل البلايستيشن

## الخطوات الأساسية للبدء

### 1. إعداد قاعدة البيانات (مطلوب مرة واحدة فقط)

1. **إنشاء حساب Supabase:**
   - اذهب إلى [supabase.com](https://supabase.com)
   - أنشئ حساب مجاني
   - أنشئ مشروع جديد

2. **إعداد قاعدة البيانات:**
   - في لوحة تحكم Supabase، اذهب إلى "SQL Editor"
   - انسخ محتوى ملف `database/schema.sql` والصقه
   - اضغط "Run" لتنفيذ الاستعلامات

3. **الحصول على مفاتيح API:**
   - اذهب إلى Settings > API
   - انسخ "Project URL" و "anon public key"

4. **إعداد متغيرات البيئة:**
   - أنشئ ملف `.env` في جذر المشروع
   - أضف المفاتيح:
   ```env
   VITE_SUPABASE_URL=your_project_url_here
   VITE_SUPABASE_ANON_KEY=your_anon_key_here
   ```

### 2. تشغيل التطبيق

```bash
# تثبيت المكتبات (مرة واحدة فقط)
npm install

# تشغيل التطبيق
npm run dev
```

### 3. الاستخدام اليومي

#### بدء جلسة جديدة:
1. اضغط "بدء جلسة جديدة" في لوحة التحكم
2. اختر عميل موجود أو أضف عميل جديد
3. اختر الجهاز المتاح
4. اضغط "بدء الجلسة"

#### إنهاء جلسة:
1. في جدول "الجلسات النشطة"
2. اضغط زر "إنهاء" للجلسة المطلوبة
3. سيتم حساب التكلفة تلقائياً

#### مراقبة الإحصائيات:
- لوحة التحكم تعرض الإحصائيات في الوقت الفعلي
- تحديث تلقائي كل 30 ثانية
- عرض الجلسات النشطة والتكلفة الحالية

## البيانات الأولية المدرجة تلقائياً

### أنواع الأجهزة:
- PlayStation 4: 15 ج.م/ساعة
- PlayStation 5: 25 ج.م/ساعة
- Xbox Series X: 20 ج.م/ساعة
- Gaming PC: 30 ج.م/ساعة

### الأجهزة:
- 4 أجهزة PS4 (PS4-1 إلى PS4-4)
- 2 جهاز PS5 (PS5-1, PS5-2)
- 2 جهاز Xbox (Xbox-1, Xbox-2)
- 3 أجهزة PC (PC-1 إلى PC-3)

## نصائح مهمة

### الأمان:
- لا تشارك ملف `.env` مع أحد
- احتفظ بنسخة احتياطية من مفاتيح Supabase

### الاستخدام:
- تأكد من إنهاء الجلسات بشكل صحيح
- راقب الجلسات النشطة بانتظام
- استخدم أسماء واضحة للعملاء

### الصيانة:
- تحقق من حالة الأجهزة بانتظام
- راجع التقارير اليومية
- احتفظ بنسخة احتياطية من البيانات

## حل المشاكل الشائعة

### التطبيق لا يعمل:
1. تأكد من تثبيت Node.js
2. تحقق من ملف `.env`
3. تأكد من تشغيل استعلامات قاعدة البيانات

### لا تظهر البيانات:
1. تحقق من اتصال الإنترنت
2. تأكد من صحة مفاتيح Supabase
3. تحقق من إعدادات قاعدة البيانات

### خطأ في بدء الجلسة:
1. تأكد من توفر أجهزة متاحة
2. تحقق من بيانات العميل
3. راجع حالة الجهاز المختار

## الدعم

للحصول على المساعدة:
1. راجع ملف `README.md` للتفاصيل الكاملة
2. تحقق من ملف `database/README.md` لمشاكل قاعدة البيانات
3. أنشئ Issue في GitHub للمشاكل التقنية

---

**نصيحة**: احتفظ بهذا الدليل في مكان سهل الوصول للرجوع إليه عند الحاجة!
