# نظام إدارة الوقت لمحل البلايستيشن

نظام شامل لإدارة الوقت والجلسات في محل البلايستيشن، مبني بتقنيات حديثة لتوفير تجربة مستخدم ممتازة وإدارة فعالة للأعمال.

## المميزات الرئيسية

### 🎮 إدارة الجلسات
- بدء وإنهاء الجلسات بسهولة
- تتبع الوقت في الزمن الفعلي
- حساب التكلفة تلقائياً
- عرض الجلسات النشطة

### 👥 إدارة العملاء
- إضافة عملاء جدد
- تتبع تاريخ الجلسات
- إدارة الأرصدة
- إحصائيات العملاء

### 🖥️ إدارة الأجهزة
- تتبع حالة الأجهزة (متاح/مشغول/صيانة)
- أنواع أجهزة متعددة (PS4, PS5, Xbox, PC)
- أسعار مختلفة لكل نوع جهاز
- ربط الجلسات بالأجهزة

### 📊 التقارير والإحصائيات
- إحصائيات يومية/أسبوعية/شهرية
- تقارير الإيرادات
- أكثر الأجهزة استخداماً
- تحليل أوقات الذروة

### 💰 النظام المالي
- حساب التكلفة تلقائياً
- تتبع المعاملات المالية
- إدارة الأرصدة
- تقارير مالية مفصلة

## التقنيات المستخدمة

- **Frontend**: React 18 + Vite
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Icons**: Lucide React
- **Routing**: React Router DOM
- **Date Handling**: date-fns

## متطلبات التشغيل

- Node.js 16+
- npm أو yarn
- حساب Supabase (مجاني)

## التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd playstation-time-manager
```

### 2. تثبيت المكتبات
```bash
npm install
```

### 3. إعداد قاعدة البيانات
1. أنشئ حساب في [Supabase](https://supabase.com)
2. أنشئ مشروع جديد
3. اذهب إلى SQL Editor وقم بتشغيل محتوى ملف `database/schema.sql`
4. احصل على Project URL و API Key من Settings > API

### 4. إعداد متغيرات البيئة
أنشئ ملف `.env` في جذر المشروع:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 5. تشغيل التطبيق
```bash
npm run dev
```

سيعمل التطبيق على `http://localhost:5173`

## هيكل المشروع

```
playstation-time-manager/
├── src/
│   ├── components/          # المكونات القابلة لإعادة الاستخدام
│   │   ├── Layout.jsx       # تخطيط الصفحة الرئيسي
│   │   └── StartSessionModal.jsx  # مودال بدء الجلسة
│   ├── pages/              # صفحات التطبيق
│   │   ├── Dashboard.jsx    # لوحة التحكم
│   │   ├── Sessions.jsx     # إدارة الجلسات
│   │   ├── Customers.jsx    # إدارة العملاء
│   │   ├── Devices.jsx      # إدارة الأجهزة
│   │   └── Reports.jsx      # التقارير
│   ├── lib/                # المكتبات والخدمات
│   │   ├── supabase.js     # إعداد Supabase
│   │   └── database.js     # خدمات قاعدة البيانات
│   ├── App.jsx             # المكون الرئيسي
│   └── main.jsx            # نقطة الدخول
├── database/
│   ├── schema.sql          # هيكل قاعدة البيانات
│   └── README.md           # دليل إعداد قاعدة البيانات
└── public/                 # الملفات العامة
```

## الاستخدام

### بدء جلسة جديدة
1. اضغط على "بدء جلسة جديدة" في لوحة التحكم
2. اختر العميل أو أضف عميل جديد
3. اختر الجهاز المتاح
4. اضغط "بدء الجلسة"

### إنهاء جلسة
1. في لوحة التحكم، ابحث عن الجلسة النشطة
2. اضغط على زر "إنهاء"
3. سيتم حساب التكلفة النهائية تلقائياً

### إضافة عميل جديد
- يمكن إضافة عميل جديد أثناء بدء الجلسة
- أو من صفحة إدارة العملاء (قيد التطوير)

## الحالة الحالية للمشروع

✅ **مكتمل:**
- إعداد بيئة التطوير
- تصميم قاعدة البيانات
- واجهة المستخدم الأساسية
- لوحة التحكم التفاعلية
- نظام بدء وإنهاء الجلسات

🚧 **قيد التطوير:**
- صفحة إدارة العملاء المفصلة
- صفحة إدارة الأجهزة
- نظام التقارير المتقدم
- النظام المالي المتكامل

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. عمل commit للتغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في GitHub.

---

**ملاحظة**: هذا المشروع في مرحلة التطوير النشط. بعض الميزات قد تكون قيد التطوير.
